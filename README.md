# Pasted Roblox External

# READ ME FIRST
- I will NOT be giving any sort of support with this. If you do not know what to do, I suggest you go take some lessions. This source has been passed around so many times so this included nothing too special.
# Changelogs
- Rewrite some of the old ESP rendering to make it a little better than the original but this wasnt much of an improvement compared to what is possible.
- Added some "supe duper cool skid" features which will be visable compared to the original "silent" paste source. I do not need this source anymore and frankly i have no need to keep it locally on my system.
- Fixed the old key -> json code as it was a little broken. The .json file saves into your LOCAL APPDATA  directory. The filename and the name of the .json can be changed to your liking. This isnt hard so dont worry!!
# Others
- Update the offsets every roblox update, This is kinda needed for the cheat to run. I will not be providing a method / way of obtaining these offsets but there are methods out there if you look well enough or you know or have a basic understanding of reverse engineering. Id suggest looking for other methods of obtaining offsets for different games and replicating such method in this case with the appropriate actions.
- Fix the protection methods as well as these are dogshit and do NOT work!! You can simply write your own ANTI-VM Code, Antidump, Encryption or Int check code pretty easily if you know how.
# Others
- Credits to the original owners of ATG and SILENCE for the base code.
