﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="utils\logs\logs.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="utils\datamodel\datamodel.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="roblox\globals\globals.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="roblox\classes\classes.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="roblox\aimbot\aimbot.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="utils\overlay\overlay.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="utils\overlay\imgui\imgui.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="utils\overlay\imgui\imgui_demo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="utils\overlay\imgui\imgui_draw.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="utils\overlay\imgui\imgui_impl_win32.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="utils\overlay\imgui\imgui_tables.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="utils\overlay\imgui\imgui_widgets.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="utils\overlay\imgui\imgui_impl_dx11.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="utils\configs\configs.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="mapper\drv_image\drv_image.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="mapper\kernel_ctx\kernel_ctx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lua_env\lua\liolib.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lua_env\lua\llex.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lua_env\lua\lmathlib.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lua_env\lua\lmem.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lua_env\lua\loadlib.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lua_env\lua\lobject.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lua_env\lua\lopcodes.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lua_env\lua\loslib.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lua_env\lua\lapi.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lua_env\lua\lauxlib.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lua_env\lua\lbaselib.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lua_env\lua\lcode.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lua_env\lua\lparser.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lua_env\lua\lstate.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lua_env\lua\lstring.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lua_env\lua\lstrlib.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lua_env\lua\ltable.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lua_env\lua\ltablib.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lua_env\lua\ltm.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lua_env\lua\lundump.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lua_env\lua\lutf8lib.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lua_env\lua\lvm.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lua_env\lua\lzio.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lua_env\lua\lcorolib.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lua_env\lua\lctype.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lua_env\lua\ldblib.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lua_env\lua\ldebug.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lua_env\lua\ldo.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lua_env\lua\ldump.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lua_env\lua\lfunc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lua_env\lua\lgc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lua_env\lua\linit.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lua_env\LuaVM.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lua_env\lua_overlay.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="utils\overlay\imgui\TextEditor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="roblox\esp\esp.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="utils\overlay\imgui\imgui_toggle.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="utils\overlay\imgui\imgui_toggle_palette.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="utils\overlay\imgui\imgui_toggle_presets.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="utils\overlay\imgui\imgui_toggle_renderer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="protection\protect\nodeserver\node_protect.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="protection\protect\selfcode\filler.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="protection\protect\selfcode\ntapi.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="protection\protect\selfcode\pe_header.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="protection\protect\selfcode\remap.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="protection\protect\selfcode\selfcode.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="protection\protect\anti_attach.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="protection\protect\anti_debugger.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="protection\protect\anti_dump.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="protection\protect\integrity_check.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="protection\protect\kill_process.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="protection\protect\protectmain.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="utils\logs\logs.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="utils\datamodel\datamodel.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="roblox\globals\globals.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="roblox\classes\classes.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="roblox\aimbot\aimbot.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="utils\overlay\overlay.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="utils\overlay\imgui\imconfig.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="utils\overlay\imgui\imgui.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="utils\overlay\imgui\imgui_impl_win32.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="utils\overlay\imgui\imgui_internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="utils\overlay\imgui\imstb_rectpack.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="utils\overlay\imgui\imstb_textedit.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="utils\overlay\imgui\imstb_truetype.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="utils\overlay\imgui\imgui_impl_dx11.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="roblox\esp\esp.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="roblox\driver\driver_impl.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="utils\overlay\ckeybind\keybind.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="utils\skcrypt\skStr.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="utils\overlay\keyauth\utils.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="utils\overlay\keyauth\json.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="utils\xorstr\xorstr.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="utils\json\json.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="utils\configs\configs.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="mapper\loadup.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="mapper\physmeme.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="mapper\raw_driver.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="mapper\drv_image\drv_image.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="mapper\kernel_ctx\kernel_ctx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="mapper\physmeme\physmeme.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="mapper\util\hook.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="mapper\util\nt.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="mapper\util\util.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="mapper\driver_data.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\lua\lprefix.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\lua\lstate.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\lua\lstring.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\lua\ltable.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\lua\ltm.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\lua\lua.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\lua\luaconf.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\lua\lualib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\lua\lundump.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\lua\lvm.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\lua\lzio.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\lua\lcode.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\lua\lctype.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\lua\ldebug.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\lua\ldo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\lua\lfunc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\lua\lgc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\lua\ljumptab.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\lua\llex.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\lua\llimits.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\lua\lmem.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\lua\lobject.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\lua\lopcodes.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\lua\lopnames.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\lua\lapi.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\lua\lauxlib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\lua\lua.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\lua\lparser.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lua_env\lua_overlay.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lua_env\LuaVM.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="keyauth\auth.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="keyauth\json.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="keyauth\skStr.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="keyauth\utils.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="utils\overlay\imgui\imgui_toggle_renderer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="utils\overlay\imgui\imgui_toggle.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="utils\overlay\imgui\imgui_toggle_math.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="utils\overlay\imgui\imgui_toggle_palette.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="utils\overlay\imgui\imgui_toggle_presets.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="utils\overlay\imgui\imgui_offset_rect.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\curl\curl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\curl\curlver.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\curl\easy.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\curl\header.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\curl\mprintf.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\curl\multi.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\curl\options.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\curl\stdcheaders.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\curl\system.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\curl\typecheck-gcc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\curl\urlapi.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\curl\websockets.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\sol\config.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\sol\forward.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\sol\sol.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="protection\encryption\includes.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="protection\encryption\lazy.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="protection\encryption\obstcate.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="protection\encryption\process.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="protection\encryption\xor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="protection\protect\nodeserver\node_protect.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="protection\protect\selfcode\filler.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="protection\protect\selfcode\ntapi.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="protection\protect\selfcode\pe_header.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="protection\protect\selfcode\remap.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="protection\protect\selfcode\selfcode.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="protection\protect\anti_attach.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="protection\protect\anti_debugger.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="protection\protect\anti_dump.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="protection\protect\integrity_check.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="protection\protect\kill_process.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="protection\protect\protectmain.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Library Include="libraries\libcurl.lib" />
    <Library Include="libraries\library_x64.lib" />
  </ItemGroup>
  <ItemGroup>
    <None Include="silence.json" />
    <None Include="include\curl\Makefile.am" />
    <None Include="include\curl\Makefile.in" />
    <None Include="fent.aps" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="fent.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <Image Include="..\..\Downloads\MDA-fentanyl_structure.ico">
      <Filter>Resource Files</Filter>
    </Image>
  </ItemGroup>
</Project>