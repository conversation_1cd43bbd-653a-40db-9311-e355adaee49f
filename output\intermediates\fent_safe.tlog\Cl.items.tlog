C:\Users\<USER>\Desktop\roblox-external-source-main\lua_env\LuaVM.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\LuaVM.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\lua_env\lua\lapi.c;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\lapi.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\lua_env\lua\lauxlib.c;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\lauxlib.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\lua_env\lua\lbaselib.c;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\lbaselib.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\lua_env\lua\lcode.c;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\lcode.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\lua_env\lua\lcorolib.c;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\lcorolib.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\lua_env\lua\lctype.c;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\lctype.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\lua_env\lua\ldblib.c;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\ldblib.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\lua_env\lua\ldebug.c;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\ldebug.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\lua_env\lua\ldo.c;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\ldo.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\lua_env\lua\ldump.c;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\ldump.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\lua_env\lua\lfunc.c;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\lfunc.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\lua_env\lua\lgc.c;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\lgc.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\lua_env\lua\linit.c;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\linit.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\lua_env\lua\liolib.c;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\liolib.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\lua_env\lua\llex.c;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\llex.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\lua_env\lua\lmathlib.c;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\lmathlib.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\lua_env\lua\lmem.c;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\lmem.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\lua_env\lua\loadlib.c;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\loadlib.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\lua_env\lua\lobject.c;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\lobject.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\lua_env\lua\lopcodes.c;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\lopcodes.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\lua_env\lua\loslib.c;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\loslib.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\lua_env\lua\lparser.c;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\lparser.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\lua_env\lua\lstate.c;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\lstate.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\lua_env\lua\lstring.c;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\lstring.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\lua_env\lua\lstrlib.c;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\lstrlib.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\lua_env\lua\ltable.c;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\ltable.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\lua_env\lua\ltablib.c;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\ltablib.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\lua_env\lua\ltm.c;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\ltm.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\lua_env\lua\lundump.c;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\lundump.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\lua_env\lua\lutf8lib.c;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\lutf8lib.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\lua_env\lua\lvm.c;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\lvm.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\lua_env\lua\lzio.c;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\lzio.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\lua_env\lua_overlay.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\lua_overlay.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\main.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\main.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\mapper\drv_image\drv_image.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\drv_image.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\mapper\kernel_ctx\kernel_ctx.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\kernel_ctx.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\protection\protect\anti_attach.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\anti_attach.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\protection\protect\anti_debugger.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\anti_debugger.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\protection\protect\anti_dump.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\anti_dump.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\protection\protect\integrity_check.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\integrity_check.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\protection\protect\kill_process.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\kill_process.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\protection\protect\nodeserver\node_protect.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\node_protect.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\protection\protect\protectmain.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\protectmain.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\protection\protect\selfcode\filler.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\filler.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\protection\protect\selfcode\ntapi.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\ntapi.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\protection\protect\selfcode\pe_header.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\pe_header.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\protection\protect\selfcode\remap.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\remap.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\protection\protect\selfcode\selfcode.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\selfcode.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\roblox\aimbot\aimbot.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\aimbot.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\roblox\classes\classes.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\classes.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\roblox\esp\esp.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\esp.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\roblox\globals\globals.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\globals.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\roblox\offsets\offset_updater.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\offset_updater.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\utils\configs\configs.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\configs.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\utils\datamodel\datamodel.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\datamodel.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\utils\logs\logs.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\logs.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\utils\overlay\imgui\imgui.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\imgui.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\utils\overlay\imgui\imgui_demo.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\imgui_demo.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\utils\overlay\imgui\imgui_draw.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\imgui_draw.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\utils\overlay\imgui\imgui_impl_dx11.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\imgui_impl_dx11.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\utils\overlay\imgui\imgui_impl_win32.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\imgui_impl_win32.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\utils\overlay\imgui\imgui_tables.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\imgui_tables.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\utils\overlay\imgui\imgui_toggle.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\imgui_toggle.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\utils\overlay\imgui\imgui_toggle_palette.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\imgui_toggle_palette.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\utils\overlay\imgui\imgui_toggle_presets.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\imgui_toggle_presets.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\utils\overlay\imgui\imgui_toggle_renderer.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\imgui_toggle_renderer.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\utils\overlay\imgui\imgui_widgets.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\imgui_widgets.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\utils\overlay\imgui\TextEditor.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\TextEditor.obj
C:\Users\<USER>\Desktop\roblox-external-source-main\utils\overlay\overlay.cpp;C:\Users\<USER>\Desktop\roblox-external-source-main\output\intermediates\overlay.obj
