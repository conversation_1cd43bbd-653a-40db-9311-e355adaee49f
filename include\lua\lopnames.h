/*
** $Id: lopnames.h $
** Opcode names
** See Copyright Notice in lua.h
*/

#if !defined(lopnames_h)
#define lopnames_h

#include <stddef.h>


/* ORDER OP */

static const char *const opnames[] = {
  "<PERSON>O<PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON>OA<PERSON>",
  "LOAD<PERSON>",
  "LOADK<PERSON>",
  "LOADFALSE",
  "LFALSESKIP",
  "LOADTRUE",
  "LOADNIL",
  "GETUPVAL",
  "SETUPVAL",
  "GETTABUP",
  "GETTABLE",
  "GETI",
  "GETFIELD",
  "SETTABUP",
  "SETTABLE",
  "SETI",
  "SETFIELD",
  "NEWTABLE",
  "SELF",
  "ADDI",
  "ADDK",
  "SUBK",
  "MULK",
  "MODK",
  "POWK",
  "DIVK",
  "IDIVK",
  "BANDK",
  "BORK",
  "BXOR<PERSON>",
  "SH<PERSON>",
  "<PERSON><PERSON><PERSON>",
  "ADD",
  "SUB",
  "<PERSON>U<PERSON>",
  "MOD",
  "POW",
  "DIV",
  "IDIV",
  "BA<PERSON>",
  "B<PERSON>",
  "BX<PERSON>",
  "SH<PERSON>",
  "SHR",
  "MMB<PERSON>",
  "MMBINI",
  "MMBINK",
  "UNM",
  "BNOT",
  "NOT",
  "LEN",
  "CONCAT",
  "CLOSE",
  "TBC",
  "JMP",
  "EQ",
  "LT",
  "LE",
  "EQK",
  "EQI",
  "LTI",
  "LEI",
  "GTI",
  "GEI",
  "TEST",
  "TESTSET",
  "CALL",
  "TAILCALL",
  "RETURN",
  "RETURN0",
  "RETURN1",
  "FORLOOP",
  "FORPREP",
  "TFORPREP",
  "TFORCALL",
  "TFORLOOP",
  "SETLIST",
  "CLOSURE",
  "VARARG",
  "VARARGPREP",
  "EXTRAARG",
  NULL
};

#endif

