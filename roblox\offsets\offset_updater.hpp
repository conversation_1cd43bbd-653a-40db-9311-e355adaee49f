#pragma once
#include <string>
#include <map>
#include <cstdint>
#include <Windows.h>
#include <wininet.h>
#pragma comment(lib, "wininet.lib")

namespace silence {
    namespace offset_updater {

        class RbxStatsClient {
        private:
            std::string api_key;

            // Helper function to perform HTTP GET request
            std::string perform_get_request(const std::string& url) const {
                HINTERNET hInternet = InternetOpenA("RbxStatsClient", INTERNET_OPEN_TYPE_DIRECT, NULL, NULL, 0);
                if (!hInternet) return "";

                HINTERNET hConnect = InternetOpenUrlA(hInternet, url.c_str(), NULL, 0, INTERNET_FLAG_RELOAD, 0);
                if (!hConnect) {
                    InternetCloseHandle(hInternet);
                    return "";
                }

                std::string response;
                char buffer[4096];
                DWORD bytesRead;
                while (InternetReadFile(hConnect, buffer, sizeof(buffer) - 1, &bytesRead) && bytesRead != 0) {
                    buffer[bytesRead] = '\0';
                    response.append(buffer);
                }

                InternetCloseHandle(hConnect);
                InternetCloseHandle(hInternet);
                return response;
            }

            // Simple JSON parsing for offset values
            std::map<std::string, std::uint32_t> parse_offsets(const std::string& response) const {
                std::map<std::string, std::uint32_t> offsets;

                // Basic JSON parsing - look for offset patterns
                size_t pos = 0;
                while ((pos = response.find("\"", pos)) != std::string::npos) {
                    size_t start = pos + 1;
                    size_t end = response.find("\"", start);
                    if (end == std::string::npos) break;

                    std::string key = response.substr(start, end - start);
                    pos = end + 1;

                    // Look for the value
                    size_t colon = response.find(":", pos);
                    if (colon == std::string::npos) continue;

                    size_t value_start = response.find_first_not_of(" \t\n\r", colon + 1);
                    if (value_start == std::string::npos) continue;

                    if (response[value_start] == '"') {
                        // String value
                        value_start++;
                        size_t value_end = response.find("\"", value_start);
                        if (value_end != std::string::npos) {
                            std::string value_str = response.substr(value_start, value_end - value_start);
                            try {
                                // Try to parse as hex
                                if (value_str.find("0x") == 0) {
                                    offsets[key] = std::stoul(value_str, nullptr, 16);
                                } else {
                                    offsets[key] = std::stoul(value_str, nullptr, 10);
                                }
                            } catch (...) {
                                // Skip invalid values
                            }
                        }
                        pos = value_end + 1;
                    } else {
                        // Numeric value
                        size_t value_end = response.find_first_of(",}\n", value_start);
                        if (value_end != std::string::npos) {
                            std::string value_str = response.substr(value_start, value_end - value_start);
                            try {
                                offsets[key] = std::stoul(value_str);
                            } catch (...) {
                                // Skip invalid values
                            }
                        }
                        pos = value_end;
                    }
                }

                return offsets;
            }

        public:
            explicit RbxStatsClient(const std::string& key) : api_key(key) {}

            std::map<std::string, std::uint32_t> get_all_offsets() {
                if (api_key.empty()) {
                    // Return empty map if no API key
                    return {};
                }

                std::string url = "https://api.rbxstats.xyz/api/offsets?api=" + api_key;
                std::string response = perform_get_request(url);

                if (response.empty()) {
                    return {};
                }

                return parse_offsets(response);
            }

            std::uint32_t get_offset_by_name(const std::string& name) {
                if (api_key.empty()) {
                    return 0;
                }

                std::string url = "https://api.rbxstats.xyz/api/offsets/search/" + name + "?api=" + api_key;
                std::string response = perform_get_request(url);

                if (response.empty()) {
                    return 0;
                }

                auto offsets = parse_offsets(response);
                auto it = offsets.find(name);
                return (it != offsets.end()) ? it->second : 0;
            }
        };

        // Global offset updater functions
        bool update_offsets_from_api(const std::string& api_key);
        void apply_fallback_offsets();
        std::uint32_t get_dynamic_offset(const std::string& offset_name);
    }
}
