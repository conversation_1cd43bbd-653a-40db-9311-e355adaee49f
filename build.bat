@echo off
echo [fent external] Building Roblox External Cheat...
echo.

REM Check if Visual Studio is installed
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\Common7\Tools\VsDevCmd.bat" (
    echo Found Visual Studio 2019 Community
    call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\Common7\Tools\VsDevCmd.bat"
    goto :build
)

if exist "C:\Program Files (x86)\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat" (
    echo Found Visual Studio 2022 Community
    call "C:\Program Files (x86)\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat"
    goto :build
)

if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat" (
    echo Found Visual Studio 2022 Community (x64)
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat"
    goto :build
)

if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\Tools\VsDevCmd.bat" (
    echo Found Visual Studio 2019 Professional
    call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\Tools\VsDevCmd.bat"
    goto :build
)

if exist "C:\Program Files (x86)\Microsoft Visual Studio\2022\Professional\Common7\Tools\VsDevCmd.bat" (
    echo Found Visual Studio 2022 Professional
    call "C:\Program Files (x86)\Microsoft Visual Studio\2022\Professional\Common7\Tools\VsDevCmd.bat"
    goto :build
)

echo ERROR: Visual Studio not found!
echo Please install Visual Studio 2019 or 2022 with C++ development tools
echo Or manually run the Developer Command Prompt and use:
echo msbuild "fent external workspace.sln" /p:Configuration=Release /p:Platform=x64
pause
exit /b 1

:build
echo.
echo Building solution...
msbuild "fent external workspace.sln" /p:Configuration=Release /p:Platform=x64 /m

if %ERRORLEVEL% EQU 0 (
    echo.
    echo [SUCCESS] Build completed successfully!
    echo Executable should be in: .\output\build\
    echo.
    echo IMPORTANT NOTES:
    echo 1. Make sure to run as Administrator
    echo 2. Update KeyAuth credentials in main.cpp if needed
    echo 3. Add RbxStats API key for automatic offset updates
    echo 4. Ensure Roblox is running before starting the cheat
    echo.
) else (
    echo.
    echo [ERROR] Build failed with error code %ERRORLEVEL%
    echo Check the output above for compilation errors
    echo.
)

pause
