#include "offset_updater.hpp"
#include "../classes/classes.hpp"
#include "../../utils/xorstr/xorstr.hpp"
#include <iostream>

namespace silence {
    namespace offset_updater {
        
        // Global storage for dynamic offsets
        static std::map<std::string, std::uint32_t> dynamic_offsets;
        static bool offsets_loaded = false;
        
        bool update_offsets_from_api(const std::string& api_key) {
            try {
                RbxStatsClient client(api_key);
                auto offsets = client.get_all_offsets();
                
                if (offsets.empty()) {
                    std::cout << XorStr("[fent external] > Failed to fetch offsets from API, using fallback\n");
                    apply_fallback_offsets();
                    return false;
                }
                
                dynamic_offsets = offsets;
                offsets_loaded = true;
                
                std::cout << XorStr("[fent external] > Successfully loaded ") << offsets.size() 
                         << XorStr(" offsets from API\n");
                return true;
            }
            catch (...) {
                std::cout << XorStr("[fent external] > Exception while fetching offsets, using fallback\n");
                apply_fallback_offsets();
                return false;
            }
        }
        
        void apply_fallback_offsets() {
            // Fallback to hardcoded offsets if API fails
            dynamic_offsets.clear();
            
            // Use the updated offsets from classes.hpp as fallback
            dynamic_offsets["size"] = silence::offsets::size;
            dynamic_offsets["name"] = silence::offsets::name;
            dynamic_offsets["children"] = silence::offsets::children;
            dynamic_offsets["parent"] = silence::offsets::parent;
            dynamic_offsets["local_player"] = silence::offsets::local_player;
            dynamic_offsets["model_instance"] = silence::offsets::model_instance;
            dynamic_offsets["primitive"] = silence::offsets::primitive;
            dynamic_offsets["position"] = silence::offsets::position;
            dynamic_offsets["dimensions"] = silence::offsets::dimensions;
            dynamic_offsets["viewmatrix"] = silence::offsets::viewmatrix;
            dynamic_offsets["camera_pos"] = silence::offsets::camera_pos;
            dynamic_offsets["classname"] = silence::offsets::classname;
            dynamic_offsets["health"] = silence::offsets::health;
            dynamic_offsets["max_health"] = silence::offsets::max_health;
            dynamic_offsets["walkspeed"] = silence::offsets::walkspeed;
            dynamic_offsets["jumpspeed"] = silence::offsets::jumpspeed;
            dynamic_offsets["team"] = silence::offsets::team;
            dynamic_offsets["gameid"] = silence::offsets::gameid;
            dynamic_offsets["velocity"] = silence::offsets::velocity;
            
            offsets_loaded = true;
            std::cout << XorStr("[fent external] > Using fallback offsets\n");
        }
        
        std::uint32_t get_dynamic_offset(const std::string& offset_name) {
            if (!offsets_loaded) {
                apply_fallback_offsets();
            }
            
            auto it = dynamic_offsets.find(offset_name);
            if (it != dynamic_offsets.end()) {
                return it->second;
            }
            
            // Return hardcoded fallback if not found
            if (offset_name == "size") return silence::offsets::size;
            if (offset_name == "name") return silence::offsets::name;
            if (offset_name == "children") return silence::offsets::children;
            if (offset_name == "parent") return silence::offsets::parent;
            if (offset_name == "local_player") return silence::offsets::local_player;
            if (offset_name == "model_instance") return silence::offsets::model_instance;
            if (offset_name == "primitive") return silence::offsets::primitive;
            if (offset_name == "position") return silence::offsets::position;
            if (offset_name == "dimensions") return silence::offsets::dimensions;
            if (offset_name == "viewmatrix") return silence::offsets::viewmatrix;
            if (offset_name == "camera_pos") return silence::offsets::camera_pos;
            if (offset_name == "classname") return silence::offsets::classname;
            if (offset_name == "health") return silence::offsets::health;
            if (offset_name == "max_health") return silence::offsets::max_health;
            if (offset_name == "walkspeed") return silence::offsets::walkspeed;
            if (offset_name == "jumpspeed") return silence::offsets::jumpspeed;
            if (offset_name == "team") return silence::offsets::team;
            if (offset_name == "gameid") return silence::offsets::gameid;
            if (offset_name == "velocity") return silence::offsets::velocity;
            
            return 0; // Return 0 if offset not found
        }
    }
}
