#pragma once
#include <Windows.h>
#include <winternl.h>
#include <cstdint>
#include <vector>
#include <string>

// Forward declarations for kernel types

// Signature definitions for PiDDB structures
constexpr char piddb_lock_sig[] = "\x48\x8D\x0D\x00\x00\x00\x00\xE8\x00\x00\x00\x00\x4C\x8B\x8C\x24";
constexpr char piddb_lock_mask[] = "xxx????x????xxxx";

constexpr char piddb_table_sig[] = "\x48\x8D\x0D\x00\x00\x00\x00\xE8\x00\x00\x00\x00\x48\x8D\x1D\x00\x00\x00\x00\x48\x85\xC0\x0F";
constexpr char piddb_table_mask[] = "xxx????x????xxx????xxxx";

// Memory range structure
struct pmem_range_t {
    std::uintptr_t start;
    std::uintptr_t end;
};

namespace util
{
    // Global variables for memory management
    inline std::vector<pmem_range_t> pmem_ranges;
    inline std::uintptr_t page_size = 0x1000;

    // Function to get file header from raw driver data
    PIMAGE_FILE_HEADER get_file_header(void* base_addr);

    // Function to get kernel export address
    uintptr_t get_kernel_export(const char* module_name, const char* export_name);

    // Function to validate memory address
    bool is_valid(std::uintptr_t addr);

    // Memory management functions
    namespace memory {
        std::uintptr_t get_piddb_lock();
        std::uintptr_t get_piddb_table();
    }

    // Implementation for get_file_header
    inline PIMAGE_FILE_HEADER get_file_header(void* base_addr)
    {
        if (!base_addr)
            return nullptr;

        PIMAGE_DOS_HEADER dos_header = reinterpret_cast<PIMAGE_DOS_HEADER>(base_addr);
        if (dos_header->e_magic != IMAGE_DOS_SIGNATURE)
            return nullptr;

        PIMAGE_NT_HEADERS nt_headers = reinterpret_cast<PIMAGE_NT_HEADERS>(
            reinterpret_cast<std::uintptr_t>(base_addr) + dos_header->e_lfanew);

        if (nt_headers->Signature != IMAGE_NT_SIGNATURE)
            return nullptr;

        return &nt_headers->FileHeader;
    }

    // Implementation for get_kernel_export
    inline uintptr_t get_kernel_export(const char* module_name, const char* export_name)
    {
        // Load the module in usermode to get the export RVA
        HMODULE module = LoadLibraryExA(module_name, NULL, DONT_RESOLVE_DLL_REFERENCES);
        if (!module) {
            return 0;
        }

        // Get the export address (this gives us the RVA when loaded with DONT_RESOLVE_DLL_REFERENCES)
        auto export_addr = reinterpret_cast<uintptr_t>(GetProcAddress(module, export_name));
        if (!export_addr) {
            FreeLibrary(module);
            return 0;
        }

        // Convert to RVA by subtracting the module base
        auto rva = export_addr - reinterpret_cast<uintptr_t>(module);

        FreeLibrary(module);
        return rva;
    }

    // Get kernel module base address
    inline std::uintptr_t get_kernel_base(const char* module_name)
    {
        // This is a simplified implementation
        // In a real scenario, you'd need to enumerate loaded kernel modules
        // For now, we'll use a common ntoskrnl base address range
        if (strcmp(module_name, "ntoskrnl.exe") == 0) {
            // Common ntoskrnl base addresses on Windows 10/11
            std::uintptr_t common_bases[] = {
                0xFFFFF80000000000ULL,
                0xFFFFF80001000000ULL,
                0xFFFFF80002000000ULL,
                0xFFFFF80003000000ULL,
                0xFFFFF80004000000ULL
            };

            // Return the first one for now - in practice you'd verify this
            return common_bases[0];
        }
        return 0;
    }

    // Implementation for is_valid
    inline bool is_valid(std::uintptr_t addr)
    {
        // Basic validation - check if address is in valid range
        if (addr < 0x1000 || addr > 0x7FFFFFFFFFFF) {
            return false;
        }

        // Check against known memory ranges if available
        for (const auto& range : pmem_ranges) {
            if (addr >= range.start && addr <= range.end) {
                return true;
            }
        }

        // If no ranges defined, assume valid for kernel addresses
        return addr >= 0xFFFF800000000000ULL;
    }

    // Memory management implementations
    namespace memory {
        inline std::uintptr_t get_piddb_lock()
        {
            // Get ntoskrnl base address in kernel space
            auto ntoskrnl_base = get_kernel_base("ntoskrnl.exe");
            if (!ntoskrnl_base)
                return 0;

            // Load ntoskrnl in usermode to search for signature
            HMODULE ntoskrnl_user = LoadLibraryExA("ntoskrnl.exe", NULL, DONT_RESOLVE_DLL_REFERENCES);
            if (!ntoskrnl_user)
                return 0;

            // Search for PiDDBLock signature
            auto user_base = reinterpret_cast<std::uintptr_t>(ntoskrnl_user);
            auto dos_header = reinterpret_cast<PIMAGE_DOS_HEADER>(user_base);
            auto nt_headers = reinterpret_cast<PIMAGE_NT_HEADERS>(user_base + dos_header->e_lfanew);
            auto text_section = IMAGE_FIRST_SECTION(nt_headers);

            // Find .text section
            for (int i = 0; i < nt_headers->FileHeader.NumberOfSections; i++) {
                if (memcmp(text_section[i].Name, ".text", 5) == 0) {
                    auto section_start = user_base + text_section[i].VirtualAddress;
                    auto section_size = text_section[i].Misc.VirtualSize;

                    // Search for the signature
                    for (std::uintptr_t addr = section_start; addr < section_start + section_size - 16; addr++) {
                        bool match = true;
                        for (int j = 0; j < 16; j++) {
                            if (piddb_lock_mask[j] == 'x' &&
                                *reinterpret_cast<char*>(addr + j) != piddb_lock_sig[j]) {
                                match = false;
                                break;
                            }
                        }
                        if (match) {
                            // Found the signature, calculate the RVA and convert to kernel address
                            auto rva = addr - user_base;
                            auto instruction_addr = ntoskrnl_base + rva;

                            // The instruction is: lea rcx, [rip + offset]
                            // We need to read the offset and calculate the final address
                            auto offset = *reinterpret_cast<std::int32_t*>(addr + 3);
                            auto piddb_lock_addr = instruction_addr + 7 + offset; // 7 = instruction length

                            FreeLibrary(ntoskrnl_user);
                            return piddb_lock_addr;
                        }
                    }
                    break;
                }
            }

            FreeLibrary(ntoskrnl_user);
            return 0;
        }

        inline std::uintptr_t get_piddb_table()
        {
            // Get ntoskrnl base address in kernel space
            auto ntoskrnl_base = get_kernel_base("ntoskrnl.exe");
            if (!ntoskrnl_base)
                return 0;

            // Load ntoskrnl in usermode to search for signature
            HMODULE ntoskrnl_user = LoadLibraryExA("ntoskrnl.exe", NULL, DONT_RESOLVE_DLL_REFERENCES);
            if (!ntoskrnl_user)
                return 0;

            // Search for PiDDBCacheTable signature
            auto user_base = reinterpret_cast<std::uintptr_t>(ntoskrnl_user);
            auto dos_header = reinterpret_cast<PIMAGE_DOS_HEADER>(user_base);
            auto nt_headers = reinterpret_cast<PIMAGE_NT_HEADERS>(user_base + dos_header->e_lfanew);
            auto text_section = IMAGE_FIRST_SECTION(nt_headers);

            // Find .text section
            for (int i = 0; i < nt_headers->FileHeader.NumberOfSections; i++) {
                if (memcmp(text_section[i].Name, ".text", 5) == 0) {
                    auto section_start = user_base + text_section[i].VirtualAddress;
                    auto section_size = text_section[i].Misc.VirtualSize;

                    // Search for the signature
                    for (std::uintptr_t addr = section_start; addr < section_start + section_size - 23; addr++) {
                        bool match = true;
                        for (int j = 0; j < 23; j++) {
                            if (piddb_table_mask[j] == 'x' &&
                                *reinterpret_cast<char*>(addr + j) != piddb_table_sig[j]) {
                                match = false;
                                break;
                            }
                        }
                        if (match) {
                            // Found the signature, look for the lea instruction for PiDDBCacheTable
                            // The pattern has two lea instructions, we want the second one
                            auto rva = addr - user_base;
                            auto instruction_addr = ntoskrnl_base + rva;

                            // Skip to the second lea instruction (offset 14 in the pattern)
                            auto offset = *reinterpret_cast<std::int32_t*>(addr + 14 + 3);
                            auto piddb_table_addr = instruction_addr + 14 + 7 + offset; // 14 = offset to second lea, 7 = instruction length

                            FreeLibrary(ntoskrnl_user);
                            return piddb_table_addr;
                        }
                    }
                    break;
                }
            }

            FreeLibrary(ntoskrnl_user);
            return 0;
        }
    }
}
