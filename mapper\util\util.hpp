#pragma once
#include <Windows.h>
#include <winternl.h>
#include <cstdint>
#include <vector>
#include <string>

// Forward declarations for kernel types

// Memory range structure
struct pmem_range_t {
    std::uintptr_t start;
    std::uintptr_t end;
};

namespace util
{
    // Global variables for memory management
    inline std::vector<pmem_range_t> pmem_ranges;
    inline std::uintptr_t page_size = 0x1000;

    // Function to get file header from raw driver data
    PIMAGE_FILE_HEADER get_file_header(void* base_addr);

    // Function to get kernel export address
    void* get_kernel_export(const char* module_name, const char* export_name);

    // Function to validate memory address
    bool is_valid(std::uintptr_t addr);

    // Memory management functions
    namespace memory {
        std::uintptr_t get_piddb_lock();
        std::uintptr_t get_piddb_table();
    }

    // Implementation for get_file_header
    inline PIMAGE_FILE_HEADER get_file_header(void* base_addr)
    {
        if (!base_addr)
            return nullptr;

        PIMAGE_DOS_HEADER dos_header = reinterpret_cast<PIMAGE_DOS_HEADER>(base_addr);
        if (dos_header->e_magic != IMAGE_DOS_SIGNATURE)
            return nullptr;

        PIMAGE_NT_HEADERS nt_headers = reinterpret_cast<PIMAGE_NT_HEADERS>(
            reinterpret_cast<std::uintptr_t>(base_addr) + dos_header->e_lfanew);

        if (nt_headers->Signature != IMAGE_NT_SIGNATURE)
            return nullptr;

        return &nt_headers->FileHeader;
    }

    // Implementation for get_kernel_export
    inline void* get_kernel_export(const char* module_name, const char* export_name)
    {
        // Simplified implementation - in real usage this would parse kernel modules
        HMODULE module = GetModuleHandleA(module_name);
        if (!module) {
            module = LoadLibraryA(module_name);
        }

        if (module) {
            return GetProcAddress(module, export_name);
        }

        return nullptr;
    }

    // Implementation for is_valid
    inline bool is_valid(std::uintptr_t addr)
    {
        // Basic validation - check if address is in valid range
        if (addr < 0x1000 || addr > 0x7FFFFFFFFFFF) {
            return false;
        }

        // Check against known memory ranges if available
        for (const auto& range : pmem_ranges) {
            if (addr >= range.start && addr <= range.end) {
                return true;
            }
        }

        // If no ranges defined, assume valid for kernel addresses
        return addr >= 0xFFFF800000000000ULL;
    }

    // Memory management implementations
    namespace memory {
        inline std::uintptr_t get_piddb_lock()
        {
            // Placeholder implementation - would need to find actual PiDDB lock
            return 0;
        }

        inline std::uintptr_t get_piddb_table()
        {
            // Placeholder implementation - would need to find actual PiDDB table
            return 0;
        }
    }
}
