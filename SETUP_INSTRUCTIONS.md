# Roblox External Cheat - Setup Instructions

## Prerequisites

### 1. Visual Studio Installation
- Install Visual Studio 2019 or 2022 (Community edition is fine)
- Make sure to include "Desktop development with C++" workload
- Include Windows 10/11 SDK

### 2. Administrator Privileges
- The cheat MUST be run as Administrator due to driver requirements
- Disable Windows Defender real-time protection temporarily during testing

## Build Instructions

### Option 1: Using the Build Script (Recommended)
1. Open Command Prompt as Administrator
2. Navigate to the project directory
3. Run: `build.bat`

### Option 2: Manual Build
1. Open "Developer Command Prompt for VS 2019/2022" as Administrator
2. Navigate to the project directory
3. Run: `msbuild "fent external workspace.sln" /p:Configuration=Release /p:Platform=x64`

## Configuration

### 1. KeyAuth Setup (Optional for Development)
- The cheat is currently set to development mode (bypasses <PERSON><PERSON><PERSON>)
- To enable KeyAuth:
  - Set `development_mode = false` in main.cpp line 57
  - Replace placeholders in main.cpp lines 22-24 with your actual KeyAuth credentials

### 2. RbxStats API Integration (Optional)
- Get an API key from https://api.rbxstats.xyz
- Add your API key to line 102 in roblox/classes/classes.cpp
- This enables automatic offset updates

### 3. Offset Updates
- Current offsets have been updated for newer Roblox versions
- If the cheat doesn't work, offsets may need further adjustment
- Check the reference repository for latest offsets: https://github.com/Jermy-tech/rbxstats_cpp

## Running the Cheat

### 1. Pre-Launch
- Start Roblox and join any game
- Close any antivirus software temporarily
- Ensure you're running as Administrator

### 2. Launch Process
1. Run the compiled executable as Administrator
2. The cheat will:
   - Load the kernel driver
   - Initialize offset system
   - Attach to Roblox process
   - Start overlay and features

### 3. Expected Output
```
[fent external] > Running in development mode - KeyAuth bypassed
[fent external] > Development mode - skipping license check
[fent external] > Initializing offset system...
[fent external] > Using fallback offsets
[fent external] > driver has been loaded...
[fent external] > initializing
[fent external] > RBX::DataModel -> 0x[address]
[fent external] > console will be hidden in 3 seconds...
```

## Troubleshooting

### Common Issues

#### 1. "Driver failed to load"
- Run as Administrator
- Disable Windows Defender
- Ensure no other cheats/drivers are running

#### 2. "Roblox is not initialized"
- Make sure Roblox is running and in a game
- Try restarting Roblox
- Check if Roblox process name is still "RobloxPlayerBeta.exe"

#### 3. "Failed to get datamodel"
- Offsets may be outdated
- Try updating offsets manually
- Check if Roblox version changed

#### 4. Build Errors
- Ensure all dependencies are included
- Check Visual Studio installation
- Verify Windows SDK is installed

### Windows 11 Compatibility
- The driver may have issues on Windows 11
- If PiDDBCacheTable clearing fails, the cheat will continue anyway
- Some features may not work on Windows 11

## Features

### ESP (Visual)
- Player boxes
- Health bars
- Distance display
- Name tags
- Tracers

### Aimbot
- Smooth aiming
- FOV circle
- Target prediction
- Deadzone

### Miscellaneous
- Walkspeed modification
- Lua script execution
- Configuration saving

## Security Notes

### Important Warnings
- This is for educational purposes only
- Use at your own risk
- May result in account bans
- Disable real-time protection during use
- Don't use on main accounts

### Detection Risks
- External cheats are generally safer than injected ones
- Driver-based cheats have higher detection risk
- Use responsibly and sparingly

## Updates

### Keeping Offsets Current
1. Monitor Roblox updates
2. Check reference repositories for new offsets
3. Update offsets in roblox/classes/classes.hpp
4. Test thoroughly after updates

### Version Tracking
- Current target: Roblox version 658+
- Offsets updated for January 2025
- May need adjustment for future versions

## Support

### Getting Help
- Check GitHub issues in reference repositories
- Verify all setup steps were followed
- Ensure proper Administrator privileges
- Test with different Roblox games

### Contributing
- Report working offset combinations
- Share compatibility information
- Submit bug fixes and improvements

## Legal Disclaimer

This software is provided for educational and research purposes only. The authors are not responsible for any misuse or damage caused by this software. Use at your own risk and in accordance with applicable laws and terms of service.
